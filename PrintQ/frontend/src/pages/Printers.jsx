import { useState, useEffect } from 'react'
import { Plus, Printer, Edit, Trash2, ChevronDown, ChevronUp, Package } from 'lucide-react'
import api from '../utils/api'

const Printers = () => {
  const [printers, setPrinters] = useState([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingPrinter, setEditingPrinter] = useState(null)
  const [loading, setLoading] = useState(true)
  const [expandedPrinter, setExpandedPrinter] = useState(null)
  const [activePrints, setActivePrints] = useState({})

  useEffect(() => {
    fetchPrinters()
  }, [])

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchPrinters()
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [])

  const fetchPrinters = async () => {
    try {
      const printersData = await api.printers.getAll()
      setPrinters(printersData)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching printers:', error)
      setLoading(false)
    }
  }

  const handleAddPrinter = async (printerData) => {
    try {
      const newPrinter = await api.printers.create(printerData)
      setPrinters(prev => [...prev, newPrinter])
      setShowAddForm(false)
    } catch (error) {
      console.error('Error creating printer:', error)
      alert('Failed to create printer. Please try again.')
    }
  }

  const handleEditPrinter = async (printerData) => {
    try {
      const updatedPrinter = await api.printers.update(editingPrinter.id, printerData)
      setPrinters(prev =>
        prev.map(printer =>
          printer.id === editingPrinter.id ? updatedPrinter : printer
        )
      )
      setEditingPrinter(null)
    } catch (error) {
      console.error('Error updating printer:', error)
      alert('Failed to update printer. Please try again.')
    }
  }

  const handleDeletePrinter = async (printerId) => {
    if (window.confirm('Are you sure you want to delete this printer?')) {
      try {
        await api.printers.delete(printerId)
        setPrinters(prev => prev.filter(printer => printer.id !== printerId))
      } catch (error) {
        console.error('Error deleting printer:', error)
        alert('Failed to delete printer. Please try again.')
      }
    }
  }

  const fetchActivePrints = async (printerId) => {
    try {
      const prints = await api.printers.getActivePrints(printerId)
      setActivePrints(prev => ({
        ...prev,
        [printerId]: prints
      }))
    } catch (error) {
      console.error('Error fetching active prints:', error)
      alert('Failed to fetch active prints. Please try again.')
    }
  }

  const handleToggleExpand = async (printerId) => {
    if (expandedPrinter === printerId) {
      // Collapse
      setExpandedPrinter(null)
    } else {
      // Expand
      setExpandedPrinter(printerId)
      // Fetch active prints if not already loaded
      if (!activePrints[printerId]) {
        await fetchActivePrints(printerId)
      }
    }
  }





  const PrinterForm = ({ printer, onSubmit, onCancel }) => {
    const [formData, setFormData] = useState({
      name: printer?.name || ''
    })

    const handleSubmit = (e) => {
      e.preventDefault()
      if (!formData.name.trim()) {
        alert('Please enter a printer name')
        return
      }
      onSubmit(formData)
    }

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {printer ? 'Edit Printer' : 'Add New Printer'}
            </h2>
          </div>
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Printer Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="input-field"
                placeholder="Enter printer name"
                required
              />
            </div>
            


            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
              >
                {printer ? 'Update Printer' : 'Add Printer'}
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Printers</h1>
          <p className="text-gray-600">Manage your 3D printers and monitor their status</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Printer</span>
        </button>
      </div>

      {/* Printers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {printers.map((printer) => (
          <div key={printer.id} className="card p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center space-x-3">
                <Printer className="h-8 w-8 text-gray-400" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{printer.name}</h3>
                </div>
              </div>
              
              <div className="flex space-x-1">
                <button
                  onClick={() => setEditingPrinter(printer)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeletePrinter(printer.id)}
                  className="text-gray-400 hover:text-error-600"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Current Job */}
            {printer.currentJob ? (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-1">Current Job</h4>
                <p className="text-sm text-gray-600">{printer.currentJob}</p>
              </div>
            ) : (
              <div className="mb-4">
                <p className="text-sm text-gray-500 italic">No active job</p>
              </div>
            )}



            {/* Active Prints Toggle Button */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={() => handleToggleExpand(printer.id)}
                className="w-full flex items-center justify-between text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <Package className="h-4 w-4" />
                  <span>Active Prints</span>
                  {activePrints[printer.id] && (
                    <span className="text-xs text-gray-500">
                      ({activePrints[printer.id].length})
                    </span>
                  )}
                </div>
                {expandedPrinter === printer.id ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            </div>

            {/* Active Prints List */}
            {expandedPrinter === printer.id && (
              <div className="mt-3 space-y-2">
                {activePrints[printer.id] ? (
                  activePrints[printer.id].length > 0 ? (
                    activePrints[printer.id].map((print) => (
                      <div key={`${print.jobId}-${print.id}`} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h5 className="text-sm font-medium text-gray-900">{print.name}</h5>
                            <p className="text-xs text-gray-600">{print.jobName} - {print.clientName}</p>
                          </div>
                        </div>
                        <div className="flex justify-between items-center text-xs text-gray-600">
                          <span>Qty: {print.quantity}</span>
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-3 h-3 rounded-full border border-gray-300"
                              style={{ backgroundColor: print.color }}
                            />
                            <span>{print.material}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500 italic text-center py-2">No active prints</p>
                  )
                ) : (
                  <div className="flex justify-center py-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {printers.length === 0 && (
        <div className="text-center py-12">
          <Printer className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No printers</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first 3D printer.
          </p>
          <button
            onClick={() => setShowAddForm(true)}
            className="mt-4 btn-primary"
          >
            Add Printer
          </button>
        </div>
      )}

      {/* Add/Edit Forms */}
      {showAddForm && (
        <PrinterForm
          onSubmit={handleAddPrinter}
          onCancel={() => setShowAddForm(false)}
        />
      )}

      {editingPrinter && (
        <PrinterForm
          printer={editingPrinter}
          onSubmit={handleEditPrinter}
          onCancel={() => setEditingPrinter(null)}
        />
      )}
    </div>
  )
}

export default Printers
