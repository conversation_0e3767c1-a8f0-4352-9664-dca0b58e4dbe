import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Printer, ChevronDown, Package, Plus, CheckCircle } from 'lucide-react'
import api from '../utils/api'
import RefreshIndicator from '../components/RefreshIndicator'

const Dashboard = () => {
  const navigate = useNavigate()
  const [uncompletedProjects, setUncompletedProjects] = useState([])
  const [selectedProject, setSelectedProject] = useState(null)
  const [printers, setPrinters] = useState([])
  const [colorOptions, setColorOptions] = useState([])
  const [allJobs, setAllJobs] = useState([])
  const [loading, setLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState(null)

  useEffect(() => {
    // Fetch dashboard data
    fetchUncompletedProjects()
    fetchPrinters()
    fetchColorOptions()
  }, [])

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchUncompletedProjects(true)
      fetchPrinters()
      fetchColorOptions()
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [])

  const fetchUncompletedProjects = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setIsRefreshing(true)
      const jobsData = await api.jobs.getAll()
      setAllJobs(jobsData)
      const uncompleted = jobsData.filter(job => job.status !== 'completed')
      setUncompletedProjects(uncompleted)
      setLoading(false)
      setLastRefresh(new Date())
    } catch (error) {
      console.error('Error fetching uncompleted projects:', error)
      setLoading(false)
    } finally {
      if (showRefreshing) setIsRefreshing(false)
    }
  }

  const fetchPrinters = async () => {
    try {
      const printersData = await api.printers.getAll()
      setPrinters(printersData)
    } catch (error) {
      console.error('Error fetching printers:', error)
    }
  }

  const fetchColorOptions = async () => {
    try {
      const settingsData = await api.settings.get()
      setColorOptions(settingsData.colors || [])
    } catch (error) {
      console.error('Error fetching color options:', error)
    }
  }

  const getPrinterName = (printerId) => {
    const printer = printers.find(p => p.id === printerId)
    return printer ? printer.name : 'Unassigned'
  }

  const getColorLabel = (colorValue) => {
    const colorOption = colorOptions.find(c => c.value === colorValue)
    return colorOption ? colorOption.label : colorValue
  }

  const getProjectPrinters = (project) => {
    if (!project || !project.parts) return {}

    const printerMap = {}

    project.parts.forEach(part => {
      const printerKey = part.printerId ? getPrinterName(part.printerId) : 'Unassigned'

      if (!printerMap[printerKey]) {
        printerMap[printerKey] = []
      }

      printerMap[printerKey].push(part)
    })

    return printerMap
  }

  const handleProjectSelect = (projectId) => {
    const project = uncompletedProjects.find(p => p.id === parseInt(projectId))
    setSelectedProject(project)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Overview of your 3D printing operations</p>
        </div>
        <RefreshIndicator isRefreshing={isRefreshing} lastRefresh={lastRefresh} />
      </div>

      {/* Project Details Section */}
      <div className="card p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Project Details</h2>
          <span className="text-sm text-gray-500">
            {uncompletedProjects.length} uncompleted project{uncompletedProjects.length !== 1 ? 's' : ''}
          </span>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : uncompletedProjects.length === 0 ? (
          // Empty state when no uncompleted projects
          <div className="text-center py-12">
            <div className="mx-auto w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6">
              {allJobs.length === 0 ? (
                <Package className="h-10 w-10 text-gray-400" />
              ) : (
                <CheckCircle className="h-10 w-10 text-green-500" />
              )}
            </div>

            {allJobs.length === 0 ? (
              <>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  Welcome to PrintQ! Create your first print job to get started with managing your 3D printing projects.
                </p>
                <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 max-w-md mx-auto">
                  <p className="text-primary-800 text-sm font-medium mb-2">Ready to get started?</p>
                  <p className="text-primary-700 text-sm">
                    Click the <span className="inline-flex items-center px-2 py-1 bg-primary-600 text-white rounded-full text-xs">
                      <Plus className="h-3 w-3" />
                    </span> button in the bottom right corner to create your first print job!
                  </p>
                </div>
              </>
            ) : (
              <>
                <h3 className="text-lg font-medium text-gray-900 mb-2">All projects completed! 🎉</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  Great job! You've completed all your print jobs. Ready to start a new project?
                </p>
                <div className="space-x-3">
                  <button
                    onClick={() => navigate('/jobs')}
                    className="btn-secondary"
                  >
                    View All Jobs
                  </button>
                  <div className="bg-primary-50 border border-primary-200 rounded-lg p-3">
                    <p className="text-primary-700 text-sm">
                      Use the <span className="inline-flex items-center px-2 py-1 bg-primary-600 text-white rounded-full text-xs">
                        <Plus className="h-3 w-3" />
                      </span> button to start a new project!
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
        ) : (
          <>
            {/* Project Dropdown */}
            <div className="mb-6">
              <label htmlFor="project-select" className="block text-sm font-medium text-gray-700 mb-2">
                Select Uncompleted Project
              </label>
              <div className="relative">
                <select
                  id="project-select"
                  value={selectedProject?.id || ''}
                  onChange={(e) => handleProjectSelect(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md appearance-none bg-white"
                >
                  <option value="">Select a project...</option>
                  {uncompletedProjects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name} - {project.clientName}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          </>
        )}

        {/* Project Details Display - Only show if we have uncompleted projects */}
        {!loading && uncompletedProjects.length > 0 && (
          <>
            {selectedProject && (
              <div className="space-y-4">
                <div className="border-t border-gray-200 pt-4">
                  <h3 className="text-md font-medium text-gray-900 mb-3">
                    {selectedProject.name} - Printers & Parts
                  </h3>

                  {Object.entries(getProjectPrinters(selectedProject)).map(([printerName, parts]) => (
                    <div key={printerName} className="mb-4 p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-3">
                        <Printer className="h-5 w-5 text-gray-600" />
                        <h4 className="font-medium text-gray-900">{printerName}</h4>
                        <span className="text-sm text-gray-500">({parts.length} part{parts.length !== 1 ? 's' : ''})</span>
                      </div>

                      <div className="space-y-2">
                        {parts.map((part) => (
                          <div key={part.id} className="flex items-center justify-between p-3 bg-white rounded border">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3">
                                <span className="font-medium text-gray-900">{part.name}</span>
                                <span className="text-sm text-gray-600">Qty: {part.quantity}</span>
                                {part.material && (
                                  <span className="text-sm text-gray-600">Material: {part.material}</span>
                                )}
                              </div>
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-sm text-gray-600">Colors:</span>
                                <div className="flex flex-wrap gap-1">
                                  {(part.colors || [part.color]).map((color, index) => (
                                    <span
                                      key={index}
                                      className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded"
                                    >
                                      {getColorLabel(color)}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!selectedProject && (
              <div className="text-center py-8 text-gray-500">
                <Printer className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <p>Select a project to view its printers and parts details</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default Dashboard
